import { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import {
  Box,
  Button,
  TextField,
  Typography,
  Container,
  Alert,
  CircularProgress,
  FormControlLabel,
  Checkbox,
  Paper,
  Grid,
  useTheme,
  useMediaQuery,
  InputAdornment,
  IconButton,
  Divider,
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import authService from '../services/authService';
import HomeIcon from '@mui/icons-material/Home';
import EmailIcon from '@mui/icons-material/Email';
import LockIcon from '@mui/icons-material/Lock';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import GoogleLoginButton from '../components/GoogleLoginButton';
import ReCaptcha from '../components/ReCaptcha';

const validationSchema = Yup.object({
  email: Yup.string().email('Invalid email').required('Email is required'),
  password: Yup.string().required('Password is required'),
  captchaToken: Yup.string().required('Please complete the CAPTCHA verification'),
});

export default function Login() {
  const navigate = useNavigate();
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [captchaError, setCaptchaError] = useState('');
  const { login } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const formik = useFormik({
    initialValues: {
      email: '',
      password: '',
      rememberMe: false,
      captchaToken: '',
    },
    validationSchema,
    onSubmit: async (values) => {
      setLoading(true);
      setError('');
      try {
        const response = await authService.login(values);
        console.log('Login response:', response);

        // Check for different response formats

        // Case 1: Standard 2FA response with requiresTwoFactor flag
        if (response.requiresTwoFactor) {
          navigate('/two-factor-verify', {
            state: {
              email: values.email,
              twoFactorType: response.twoFactorType || 'authenticator'
            }
          });
          return;
        }

        // Case 2: Normal login with access token
        if (response.accessToken) {
          login(response, response.accessToken);
          navigate('/profile');
          return;
        }

        // Case 3: User has 2FA enabled but API doesn't return requiresTwoFactor flag
        // This happens when the API returns a UserResponse object without 2FA properties
        // We can detect this by checking if the response has user properties but no accessToken
        if (response.email && response.id && !response.accessToken) {
          // This is likely a user with 2FA enabled
          // Get 2FA status to confirm
          try {
            const twoFactorStatus = await authService.get2faStatus();
            if (twoFactorStatus.isEnabled) {
              navigate('/two-factor-verify', {
                state: {
                  email: values.email,
                  twoFactorType: twoFactorStatus.type || 'authenticator'
                }
              });
              return;
            }
          } catch (twoFactorErr) {
            console.error('Failed to get 2FA status:', twoFactorErr);
            // If we can't get 2FA status, assume 2FA is required
            navigate('/two-factor-verify', {
              state: {
                email: values.email,
                twoFactorType: 'authenticator' // Default to authenticator
              }
            });
            return;
          }
        }

        // If we get here, the response format is unexpected
        console.error('Unexpected login response format:', response);
        setError('Invalid response from server. Please try again or contact support.');
      } catch (err) {
        console.error('Login error:', err);
        setError(err.response?.data?.message || 'Login failed');
      } finally {
        setLoading(false);
      }
    },
  });

  const handleTogglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  const handleCaptchaChange = (token) => {
    formik.setFieldValue('captchaToken', token);
    setCaptchaError('');
  };

  const handleCaptchaExpired = () => {
    formik.setFieldValue('captchaToken', '');
    setCaptchaError('CAPTCHA has expired, please verify again');
  };

  const handleCaptchaError = () => {
    formik.setFieldValue('captchaToken', '');
    setCaptchaError('Error loading CAPTCHA, please refresh the page');
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: theme.palette.mode === 'dark'
          ? 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)'
          : 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)',
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: theme.palette.mode === 'dark'
            ? 'radial-gradient(circle at 20% 30%, rgba(129, 140, 248, 0.2) 0%, transparent 50%), radial-gradient(circle at 80% 70%, rgba(244, 114, 182, 0.15) 0%, transparent 50%)'
            : 'radial-gradient(circle at 20% 30%, rgba(79, 70, 229, 0.08) 0%, transparent 50%), radial-gradient(circle at 80% 70%, rgba(219, 39, 119, 0.06) 0%, transparent 50%)',
          zIndex: 1,
        },
      }}
    >
      <Container component="main" maxWidth="lg" sx={{ pt: { xs: 8, md: 12 }, pb: 8, position: 'relative', zIndex: 2 }}>
        <Grid container spacing={6} justifyContent="center" alignItems="center" sx={{ minHeight: '80vh' }}>
          {/* Left side - Welcome Message (hidden on mobile) */}
          {!isMobile && (
            <Grid item xs={12} sm={10} md={6} lg={7}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  height: '100%',
                  pr: { md: 6 },
                }}
                className="fade-in"
              >
                <Typography
                  variant="h1"
                  component="h1"
                  sx={{
                    fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4rem' },
                    fontWeight: 800,
                    lineHeight: 1.1,
                    mb: 4,
                    background: theme.palette.mode === 'dark'
                      ? 'linear-gradient(135deg, #f1f5f9 0%, #94a3b8 100%)'
                      : 'linear-gradient(135deg, #0f172a 0%, #475569 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                  }}
                >
                  Welcome Back to{' '}
                  <Box
                    component="span"
                    sx={{
                      background: theme.palette.mode === 'dark'
                        ? 'linear-gradient(135deg, #818cf8 0%, #f472b6 100%)'
                        : 'linear-gradient(135deg, #4f46e5 0%, #db2777 100%)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text',
                    }}
                  >
                    Prescripto
                  </Box>
                </Typography>
                <Typography
                  variant="h5"
                  paragraph
                  sx={{
                    mb: 6,
                    color: 'text.secondary',
                    fontWeight: 400,
                    lineHeight: 1.6,
                    maxWidth: '90%',
                  }}
                >
                  Access your personalized healthcare dashboard and manage your appointments with ease.
                  Our secure platform ensures your medical information is always protected.
                </Typography>

                <Paper
                  elevation={0}
                  sx={{
                    p: 4,
                    borderRadius: 5,
                    background: theme.palette.mode === 'dark'
                      ? 'linear-gradient(135deg, rgba(30, 41, 59, 0.6) 0%, rgba(51, 65, 85, 0.4) 100%)'
                      : 'linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%)',
                    backdropFilter: 'blur(20px)',
                    border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
                    boxShadow: theme.palette.mode === 'dark'
                      ? '0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -4px rgba(0, 0, 0, 0.4)'
                      : '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
                  }}
                  className="slide-up"
                >
                  <Typography
                    variant="h6"
                    sx={{
                      fontStyle: 'italic',
                      mb: 3,
                      color: 'text.primary',
                      fontWeight: 500,
                    }}
                  >
                    "Secure access to healthcare information empowers both patients and providers to make better decisions."
                  </Typography>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      textAlign: 'right',
                      color: 'primary.main',
                      fontWeight: 600,
                    }}
                  >
                    — Healthcare Innovation Team
                  </Typography>
                </Paper>
              </Box>
            </Grid>
          )}

        {/* Right side - Login Form */}
        <Grid item xs={12} sm={10} md={6} lg={5}>
          <Paper
            elevation={0}
            sx={{
              borderRadius: 6,
              overflow: 'hidden',
              p: { xs: 4, md: 6 },
              background: theme.palette.mode === 'dark'
                ? 'linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%)'
                : 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.7) 100%)',
              backdropFilter: 'blur(20px)',
              border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
              boxShadow: theme.palette.mode === 'dark'
                ? '0 25px 50px -12px rgba(0, 0, 0, 0.7)'
                : '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            }}
            className="scale-in"
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
              }}
            >
            {/* Mobile Logo */}
            {isMobile && (
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 6, justifyContent: 'center' }}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: theme.palette.mode === 'dark'
                      ? 'linear-gradient(135deg, #818cf8 0%, #a5b4fc 100%)'
                      : 'linear-gradient(135deg, #4f46e5 0%, #6366f1 100%)',
                    color: 'white',
                    p: 2,
                    borderRadius: 3,
                    mr: 2,
                    boxShadow: theme.palette.mode === 'dark'
                      ? '0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.3)'
                      : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  <HomeIcon sx={{ fontSize: 28 }} />
                </Box>
                <Typography
                  variant="h4"
                  component="h1"
                  sx={{
                    fontWeight: 700,
                    background: theme.palette.mode === 'dark'
                      ? 'linear-gradient(135deg, #818cf8 0%, #f472b6 100%)'
                      : 'linear-gradient(135deg, #4f46e5 0%, #db2777 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                  }}
                >
                  Prescripto
                </Typography>
              </Box>
            )}

            <Box sx={{ mb: 4, textAlign: isMobile ? 'center' : 'left' }}>
              <Typography
                component="h1"
                variant="h3"
                sx={{
                  fontWeight: 700,
                  mb: 2,
                  background: theme.palette.mode === 'dark'
                    ? 'linear-gradient(135deg, #f1f5f9 0%, #94a3b8 100%)'
                    : 'linear-gradient(135deg, #0f172a 0%, #475569 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                }}
              >
                Welcome Back
              </Typography>
              <Typography
                variant="h6"
                color="text.secondary"
                sx={{
                  fontWeight: 400,
                  lineHeight: 1.5,
                }}
              >
                Sign in to your account to continue
              </Typography>
            </Box>

            {error && <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>}

            <Box component="form" onSubmit={formik.handleSubmit} sx={{ mt: 1 }}>
              <Box sx={{ mb: 4 }}>
                <Typography
                  variant="subtitle1"
                  color="text.primary"
                  gutterBottom
                  sx={{ fontWeight: 600, mb: 1.5 }}
                >
                  Email Address
                </Typography>
                <TextField
                  fullWidth
                  id="email"
                  name="email"
                  placeholder="Enter your email address"
                  value={formik.values.email}
                  onChange={formik.handleChange}
                  error={formik.touched.email && Boolean(formik.errors.email)}
                  helperText={formik.touched.email && formik.errors.email}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <EmailIcon sx={{ color: 'text.secondary' }} />
                      </InputAdornment>
                    ),
                  }}
                  variant="outlined"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      backgroundColor: theme.palette.mode === 'dark'
                        ? 'rgba(255, 255, 255, 0.03)'
                        : 'rgba(0, 0, 0, 0.02)',
                      border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
                      transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                      '&:hover': {
                        backgroundColor: theme.palette.mode === 'dark'
                          ? 'rgba(255, 255, 255, 0.05)'
                          : 'rgba(0, 0, 0, 0.04)',
                        borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)',
                      },
                      '&.Mui-focused': {
                        backgroundColor: theme.palette.mode === 'dark'
                          ? 'rgba(255, 255, 255, 0.06)'
                          : 'rgba(0, 0, 0, 0.06)',
                        borderColor: 'primary.main',
                        boxShadow: theme.palette.mode === 'dark'
                          ? '0 0 0 3px rgba(129, 140, 248, 0.1)'
                          : '0 0 0 3px rgba(79, 70, 229, 0.1)',
                      },
                    },
                    '& .MuiInputBase-input': {
                      py: 2,
                      fontSize: '1rem',
                    },
                  }}
                />
              </Box>
              <Box sx={{ mb: 4 }}>
                <Typography
                  variant="subtitle1"
                  color="text.primary"
                  gutterBottom
                  sx={{ fontWeight: 600, mb: 1.5 }}
                >
                  Password
                </Typography>
                <TextField
                  fullWidth
                  id="password"
                  name="password"
                  placeholder="Enter your password"
                  type={showPassword ? 'text' : 'password'}
                  value={formik.values.password}
                  onChange={formik.handleChange}
                  error={formik.touched.password && Boolean(formik.errors.password)}
                  helperText={formik.touched.password && formik.errors.password}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LockIcon sx={{ color: 'text.secondary' }} />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={handleTogglePasswordVisibility}
                          edge="end"
                          sx={{
                            color: 'text.secondary',
                            '&:hover': {
                              color: 'primary.main',
                              backgroundColor: theme.palette.mode === 'dark'
                                ? 'rgba(129, 140, 248, 0.08)'
                                : 'rgba(79, 70, 229, 0.04)',
                            },
                          }}
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  variant="outlined"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      backgroundColor: theme.palette.mode === 'dark'
                        ? 'rgba(255, 255, 255, 0.03)'
                        : 'rgba(0, 0, 0, 0.02)',
                      border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
                      transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                      '&:hover': {
                        backgroundColor: theme.palette.mode === 'dark'
                          ? 'rgba(255, 255, 255, 0.05)'
                          : 'rgba(0, 0, 0, 0.04)',
                        borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)',
                      },
                      '&.Mui-focused': {
                        backgroundColor: theme.palette.mode === 'dark'
                          ? 'rgba(255, 255, 255, 0.06)'
                          : 'rgba(0, 0, 0, 0.06)',
                        borderColor: 'primary.main',
                        boxShadow: theme.palette.mode === 'dark'
                          ? '0 0 0 3px rgba(129, 140, 248, 0.1)'
                          : '0 0 0 3px rgba(79, 70, 229, 0.1)',
                      },
                    },
                    '& .MuiInputBase-input': {
                      py: 2,
                      fontSize: '1rem',
                    },
                  }}
                />
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      name="rememberMe"
                      checked={formik.values.rememberMe}
                      onChange={formik.handleChange}
                      color="primary"
                      size="small"
                    />
                  }
                  label={<Typography variant="body2">Remember me</Typography>}
                />
                <Typography variant="body2" component={Link} to="/forgot-password" color="primary" sx={{ textDecoration: 'none' }}>
                  Forgot password?
                </Typography>
              </Box>

              {/* CAPTCHA Component */}
              <ReCaptcha
                onChange={handleCaptchaChange}
                onExpired={handleCaptchaExpired}
                onError={handleCaptchaError}
              />
              {captchaError && (
                <Alert severity="error" sx={{ mt: 1, mb: 1 }}>
                  {captchaError}
                </Alert>
              )}
              {formik.touched.captchaToken && formik.errors.captchaToken && (
                <Alert severity="error" sx={{ mt: 1, mb: 1 }}>
                  {formik.errors.captchaToken}
                </Alert>
              )}

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                sx={{
                  mt: 6,
                  mb: 4,
                  py: 2,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  borderRadius: 4,
                  background: theme.palette.mode === 'dark'
                    ? 'linear-gradient(135deg, #818cf8 0%, #a5b4fc 100%)'
                    : 'linear-gradient(135deg, #4f46e5 0%, #6366f1 100%)',
                  boxShadow: theme.palette.mode === 'dark'
                    ? '0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -4px rgba(0, 0, 0, 0.4)'
                    : '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    background: theme.palette.mode === 'dark'
                      ? 'linear-gradient(135deg, #6366f1 0%, #818cf8 100%)'
                      : 'linear-gradient(135deg, #4338ca 0%, #4f46e5 100%)',
                    transform: 'translateY(-2px)',
                    boxShadow: theme.palette.mode === 'dark'
                      ? '0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 8px 10px -6px rgba(0, 0, 0, 0.5)'
                      : '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',
                  },
                  '&:disabled': {
                    background: theme.palette.mode === 'dark'
                      ? 'rgba(129, 140, 248, 0.3)'
                      : 'rgba(79, 70, 229, 0.3)',
                    color: 'rgba(255, 255, 255, 0.7)',
                  },
                }}
                disabled={loading}
              >
                {loading ? (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <CircularProgress size={20} color="inherit" />
                    <Typography variant="button">Signing In...</Typography>
                  </Box>
                ) : (
                  'Sign In'
                )}
              </Button>

              <Box sx={{ mt: 4, mb: 4, display: 'flex', alignItems: 'center' }}>
                <Divider
                  sx={{
                    flexGrow: 1,
                    borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)',
                  }}
                />
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    mx: 3,
                    fontWeight: 500,
                    fontSize: '0.875rem',
                  }}
                >
                  OR
                </Typography>
                <Divider
                  sx={{
                    flexGrow: 1,
                    borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)',
                  }}
                />
              </Box>

              <GoogleLoginButton
                variant="login"
                onError={(errorMessage) => setError(errorMessage)}
              />

              <Box sx={{ mt: 6, textAlign: 'center' }}>
                <Typography
                  variant="body1"
                  color="text.secondary"
                  sx={{ fontWeight: 400 }}
                >
                  Don't have an account?{' '}
                  <Link
                    to="/register"
                    style={{
                      textDecoration: 'none',
                      color: theme.palette.primary.main,
                      fontWeight: 600,
                      transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.textDecoration = 'underline';
                      e.target.style.textUnderlineOffset = '4px';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.textDecoration = 'none';
                    }}
                  >
                    Create an account
                  </Link>
                </Typography>
              </Box>
            </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
}